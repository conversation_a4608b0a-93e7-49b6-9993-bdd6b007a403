import {useQuery} from '@tanstack/react-query';
import {useEffect} from 'react';
import useUserStore from '../store/user-store';
import {retrieveUserDetails} from '../services/user-details-extraction-rn';

export default function useUserRN() {
  const {data, isLoading, error} = useQuery({
    queryKey: ['user-data-rn'],
    queryFn: () => retrieveUserDetails(),
    retry: 1,
  });

  console.log('useUserRN - data:', data);
  console.log('useUserRN - isLoading:', isLoading);
  console.log('useUserRN - error:', error);

  const {setUser, setIsLoading: setUserIsLoading} = useUserStore(
    (store) => store,
  );

  useEffect(() => {
    if (data) {
      setUser(data);
    }
  }, [data, setUser]);

  useEffect(() => {
    setUserIsLoading(isLoading);
  }, [isLoading, setUserIsLoading]);

  return {
    user: data ? data : null,
    isLoading,
  };
}
