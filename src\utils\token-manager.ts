import AsyncStorage from '@react-native-async-storage/async-storage';

/**
 * Storage keys for tokens and user data
 * Using constants ensures consistency across the application
 */
export const STORAGE_KEYS = {
  ACCESS_TOKEN: 'access',
  REFRESH_TOKEN: 'refresh',
  USER_DATA: 'userData',
} as const;

/**
 * Token interface for type safety
 */
export interface TokenPair {
  access: string;
  refresh: string;
}

/**
 * Centralized token management utility
 * This class provides a consistent interface for token operations
 * and ensures all token-related operations use the same storage keys
 */
export class TokenManager {
  /**
   * Get the access token from storage
   */
  static async getAccessToken(): Promise<string | null> {
    try {
      return await AsyncStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN);
    } catch (error) {
      console.error('Error getting access token:', error);
      return null;
    }
  }

  /**
   * Get the refresh token from storage
   */
  static async getRefreshToken(): Promise<string | null> {
    try {
      return await AsyncStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN);
    } catch (error) {
      console.error('Error getting refresh token:', error);
      return null;
    }
  }

  /**
   * Get both tokens as an object
   */
  static async getTokens(): Promise<TokenPair | null> {
    try {
      const [access, refresh] = await Promise.all([
        AsyncStorage.getItem(STORAGE_KEYS.ACCESS_TOKEN),
        AsyncStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN),
      ]);

      if (access && refresh) {
        return { access, refresh };
      }
      return null;
    } catch (error) {
      console.error('Error getting tokens:', error);
      return null;
    }
  }

  /**
   * Store both access and refresh tokens
   */
  static async setTokens(tokens: TokenPair): Promise<void> {
    try {
      await Promise.all([
        AsyncStorage.setItem(STORAGE_KEYS.ACCESS_TOKEN, tokens.access),
        AsyncStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, tokens.refresh),
      ]);
    } catch (error) {
      console.error('Error setting tokens:', error);
      throw error;
    }
  }

  /**
   * Store only the access token (for token refresh scenarios)
   */
  static async setAccessToken(token: string): Promise<void> {
    try {
      await AsyncStorage.setItem(STORAGE_KEYS.ACCESS_TOKEN, token);
    } catch (error) {
      console.error('Error setting access token:', error);
      throw error;
    }
  }

  /**
   * Clear all tokens from storage
   */
  static async clearTokens(): Promise<void> {
    try {
      await Promise.all([
        AsyncStorage.removeItem(STORAGE_KEYS.ACCESS_TOKEN),
        AsyncStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN),
      ]);
    } catch (error) {
      console.error('Error clearing tokens:', error);
      throw error;
    }
  }

  /**
   * Check if user is authenticated (has valid access token)
   */
  static async isAuthenticated(): Promise<boolean> {
    try {
      const accessToken = await TokenManager.getAccessToken();
      return !!accessToken;
    } catch (error) {
      console.error('Error checking authentication status:', error);
      return false;
    }
  }

  /**
   * Get authorization header for API requests
   */
  static async getAuthHeader(): Promise<{ Authorization: string } | {}> {
    try {
      const accessToken = await TokenManager.getAccessToken();
      if (accessToken) {
        return { Authorization: `Bearer ${accessToken}` };
      }
      return {};
    } catch (error) {
      console.error('Error getting auth header:', error);
      return {};
    }
  }

  /**
   * Clear all user-related data (tokens and user data)
   * Use this for logout functionality
   */
  static async clearAllUserData(): Promise<void> {
    try {
      await Promise.all([
        AsyncStorage.removeItem(STORAGE_KEYS.ACCESS_TOKEN),
        AsyncStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN),
        AsyncStorage.removeItem(STORAGE_KEYS.USER_DATA),
      ]);
    } catch (error) {
      console.error('Error clearing user data:', error);
      throw error;
    }
  }
}
