import React from 'react';
import {KeyboardAwareScrollView} from 'react-native-keyboard-aware-scroll-view';

import {text} from '../text';
import {components} from '../components';
import {useAppNavigation} from '../hooks';

const NewPassword: React.FC = (): JSX.Element => {
  const navigation = useAppNavigation();
  const {
    password,
    setPassword,
    confirmPassword,
    setConfirmPassword,
    passwordWarning,
    submitPassword,
    isLoading,
  } = useResetPassword();

  // Navigate to success screen when password is reset successfully
  useEffect(() => {
    // You might want to add a success state to the hook to handle this
  }, []);

  const renderStatusBar = () => {
    return <components.StatusBar />;
  };

  const renderHeader = () => {
    return <components.Header title='Reset password' goBack={true} />;
  };

  const renderContent = () => {
    return (
      <KeyboardAwareScrollView
        contentContainerStyle={{
          paddingHorizontal: 20,
          paddingTop: 25,
          paddingBottom: 20,
        }}
      >
        <text.T16
          style={{
            marginBottom: 40,
          }}
        >
          Enter new password and confirm.
        </text.T16>
        <components.InputField
          label='password'
          placeholder='••••••••'
          onChangeText={(text) => setPassword(text)}
          containerStyle={{
            marginBottom: passwordWarning.password ? 40 : 20,
          }}
          value={password}
          eyeOffIcon={true}
          secureTextEntry={true}
          warning={passwordWarning.password}
        />
        <components.InputField
          label='confirm password'
          placeholder='••••••••'
          onChangeText={(text) => setConfirmPassword(text)}
          containerStyle={{
            marginBottom: passwordWarning.confirmationPassword ? 40 : 20,
          }}
          value={confirmPassword}
          eyeOffIcon={true}
          secureTextEntry={true}
          warning={passwordWarning.confirmationPassword}
        />
        {passwordWarning.generalWarning ? (
          <text.T14
            style={{color: '#E82837', marginBottom: 20, textAlign: 'center'}}
          >
            {passwordWarning.generalWarning}
          </text.T14>
        ) : null}
        <components.Button
          title={isLoading ? 'Changing password...' : 'Change password'}
          onPress={() => {
            submitPassword();
            // Navigate to success screen - you might want to handle this in the hook
            // For now, let's navigate after a successful password change
            setTimeout(() => {
              navigation.navigate('ForgotPasswordSentEmail');
            }, 1000);
          }}
        />
      </KeyboardAwareScrollView>
    );
  };

  const renderHomeIndicator = () => {
    return <components.HomeIndicator />;
  };

  return (
    <components.SmartView>
      {renderStatusBar()}
      {renderHeader()}
      {renderContent()}
      {renderHomeIndicator()}
    </components.SmartView>
  );
};

export default NewPassword;
