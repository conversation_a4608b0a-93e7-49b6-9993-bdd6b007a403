/**
 * Validation result interface
 */
export interface ValidationResult {
  isValid: boolean;
  message: string;
}

/**
 * Form validation result interface
 */
export interface FormValidationResult<T> {
  isValid: boolean;
  errors: Partial<Record<keyof T, string>>;
  generalError?: string;
}

/**
 * Comprehensive validation utility
 * This ensures consistent validation rules across the application
 */
export class ValidationHelpers {
  /**
   * Email validation with comprehensive regex
   */
  static validateEmail(email: string): ValidationResult {
    if (!email || email.trim() === '') {
      return {
        isValid: false,
        message: 'Email is required',
      };
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return {
        isValid: false,
        message: 'Please enter a valid email address',
      };
    }

    return {
      isValid: true,
      message: '',
    };
  }

  /**
   * Password validation with strength requirements
   */
  static validatePassword(password: string): ValidationResult {
    if (!password || password.trim() === '') {
      return {
        isValid: false,
        message: 'Password is required',
      };
    }

    if (password.length < 8) {
      return {
        isValid: false,
        message: 'Password must be at least 8 characters long',
      };
    }

    // Check for at least one uppercase letter
    if (!/[A-Z]/.test(password)) {
      return {
        isValid: false,
        message: 'Password must contain at least one uppercase letter',
      };
    }

    // Check for at least one lowercase letter
    if (!/[a-z]/.test(password)) {
      return {
        isValid: false,
        message: 'Password must contain at least one lowercase letter',
      };
    }

    // Check for at least one number
    if (!/\d/.test(password)) {
      return {
        isValid: false,
        message: 'Password must contain at least one number',
      };
    }

    return {
      isValid: true,
      message: '',
    };
  }

  /**
   * Password confirmation validation
   */
  static validatePasswordConfirmation(password: string, confirmPassword: string): ValidationResult {
    if (!confirmPassword || confirmPassword.trim() === '') {
      return {
        isValid: false,
        message: 'Password confirmation is required',
      };
    }

    if (password !== confirmPassword) {
      return {
        isValid: false,
        message: 'Passwords do not match',
      };
    }

    return {
      isValid: true,
      message: '',
    };
  }

  /**
   * Name validation (first name, last name)
   */
  static validateName(name: string, fieldName: string = 'Name'): ValidationResult {
    if (!name || name.trim() === '') {
      return {
        isValid: false,
        message: `${fieldName} is required`,
      };
    }

    if (name.length < 2) {
      return {
        isValid: false,
        message: `${fieldName} must be at least 2 characters long`,
      };
    }

    if (name.length > 50) {
      return {
        isValid: false,
        message: `${fieldName} must be less than 50 characters`,
      };
    }

    // Check for valid characters (letters, spaces, hyphens, apostrophes)
    if (!/^[a-zA-Z\s\-']+$/.test(name)) {
      return {
        isValid: false,
        message: `${fieldName} can only contain letters, spaces, hyphens, and apostrophes`,
      };
    }

    return {
      isValid: true,
      message: '',
    };
  }

  /**
   * Phone number validation
   */
  static validatePhoneNumber(phone: string): ValidationResult {
    if (!phone || phone.trim() === '') {
      return {
        isValid: false,
        message: 'Phone number is required',
      };
    }

    // Remove all non-digit characters for validation
    const digitsOnly = phone.replace(/\D/g, '');

    if (digitsOnly.length < 10) {
      return {
        isValid: false,
        message: 'Phone number must be at least 10 digits',
      };
    }

    if (digitsOnly.length > 15) {
      return {
        isValid: false,
        message: 'Phone number must be less than 15 digits',
      };
    }

    return {
      isValid: true,
      message: '',
    };
  }

  /**
   * OTP/Verification code validation
   */
  static validateOTP(otp: string, expectedLength: number = 6): ValidationResult {
    if (!otp || otp.trim() === '') {
      return {
        isValid: false,
        message: 'Verification code is required',
      };
    }

    if (otp.length !== expectedLength) {
      return {
        isValid: false,
        message: `Verification code must be ${expectedLength} digits`,
      };
    }

    if (!/^\d+$/.test(otp)) {
      return {
        isValid: false,
        message: 'Verification code can only contain numbers',
      };
    }

    return {
      isValid: true,
      message: '',
    };
  }

  /**
   * Address validation
   */
  static validateAddress(address: string): ValidationResult {
    if (!address || address.trim() === '') {
      return {
        isValid: false,
        message: 'Address is required',
      };
    }

    if (address.length < 10) {
      return {
        isValid: false,
        message: 'Address must be at least 10 characters long',
      };
    }

    if (address.length > 200) {
      return {
        isValid: false,
        message: 'Address must be less than 200 characters',
      };
    }

    return {
      isValid: true,
      message: '',
    };
  }

  /**
   * Generic required field validation
   */
  static validateRequired(value: string, fieldName: string): ValidationResult {
    if (!value || value.trim() === '') {
      return {
        isValid: false,
        message: `${fieldName} is required`,
      };
    }

    return {
      isValid: true,
      message: '',
    };
  }

  /**
   * Validate multiple fields at once
   */
  static validateForm<T extends Record<string, any>>(
    data: T,
    validators: Partial<Record<keyof T, (value: any) => ValidationResult>>
  ): FormValidationResult<T> {
    const errors: Partial<Record<keyof T, string>> = {};
    let isValid = true;

    for (const [field, validator] of Object.entries(validators)) {
      if (validator && typeof validator === 'function') {
        const result = validator(data[field]);
        if (!result.isValid) {
          errors[field as keyof T] = result.message;
          isValid = false;
        }
      }
    }

    return {
      isValid,
      errors,
      generalError: isValid ? undefined : 'Please fix the errors above',
    };
  }
}
