import {POST} from '../../../lib/http-methods';
import {UserSignUpType} from '../types';
import {castToSignUpServerSideType} from '../utils/data-utils/types-casting/user';
import {AxiosError} from 'axios';

type AuthResponse = {
  status: number;
  ok: boolean;
  errors?: string;
};

export async function signUp(data: UserSignUpType): Promise<AuthResponse> {
  try {
    await POST(`/auth/register`, {}, castToSignUpServerSideType(data));

    return {status: 204, ok: true};
  } catch (error) {
    const axiosError = error as AxiosError;

    return {
      status: axiosError?.response?.status as number,
      errors: '',
      ok: false,
    };
  }
}
