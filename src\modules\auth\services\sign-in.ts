import {POST} from '../../../lib/http-methods';
import {UserSignInType} from '../types';
import {AxiosError, AxiosHeaders, AxiosResponse} from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';

type AuthResponse = {
  status: number;
  ok: boolean;
  errors?: string;
};

export async function signIn(data: UserSignInType): Promise<AuthResponse> {
  const headers = {} as AxiosHeaders;

  try {
    const res: AxiosResponse = await POST(`/auth/login`, headers, data);

    const tokens = res.data as {access: string; refresh: string};

    await AsyncStorage.setItem('access', tokens.access);
    await AsyncStorage.setItem('refresh', tokens.refresh);

    return {status: 204, ok: true};
  } catch (error) {
    const axiosError = error as AxiosError;

    const responseStatus = axiosError?.response?.status
      ? axiosError?.response?.status
      : 500;

    return {
      status: responseStatus,
      errors: '',
      ok: false,
    };
  }
}
