import { AxiosError } from 'axios';
import { API_STATUS_CODES, getErrorMessage } from '../config/api-endpoints';

/**
 * Standard error response interface
 */
export interface ErrorResponse {
  message: string;
  statusCode: number;
  details?: any;
}

/**
 * API Error class for consistent error handling
 */
export class APIError extends Error {
  public statusCode: number;
  public details?: any;

  constructor(message: string, statusCode: number, details?: any) {
    super(message);
    this.name = 'APIError';
    this.statusCode = statusCode;
    this.details = details;
  }
}

/**
 * Comprehensive error handler utility
 */
export class ErrorHandler {
  /**
   * Handle Axios errors and convert them to standardized format
   */
  static handleAxiosError(error: AxiosError): ErrorResponse {
    const statusCode = error.response?.status || 500;
    const message = getErrorMessage(statusCode);
    const details = error.response?.data;

    // Log error for debugging
    console.error('API Error:', {
      url: error.config?.url,
      method: error.config?.method,
      status: statusCode,
      message: error.message,
      data: details,
    });

    return {
      message,
      statusCode,
      details,
    };
  }

  /**
   * Handle general errors
   */
  static handleGeneralError(error: Error): ErrorResponse {
    console.error('General Error:', error);

    return {
      message: 'An unexpected error occurred',
      statusCode: 500,
      details: error.message,
    };
  }

  /**
   * Handle network errors
   */
  static handleNetworkError(): ErrorResponse {
    console.error('Network Error: No internet connection');

    return {
      message: 'Network error. Please check your connection.',
      statusCode: 0,
    };
  }

  /**
   * Log API request for debugging
   */
  static logAPIRequest(method: string, url: string, data?: any, headers?: any): void {
    console.log('API Request:', {
      method: method.toUpperCase(),
      url,
      data: data ? JSON.stringify(data) : 'No data',
      headers: headers ? JSON.stringify(headers) : 'No headers',
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Log API response for debugging
   */
  static logAPIResponse(url: string, status: number, data?: any): void {
    console.log('API Response:', {
      url,
      status,
      data: data ? JSON.stringify(data) : 'No data',
      timestamp: new Date().toISOString(),
    });
  }

  /**
   * Check if error is a network error
   */
  static isNetworkError(error: any): boolean {
    return !error.response && error.request;
  }

  /**
   * Check if error is an authentication error
   */
  static isAuthError(error: any): boolean {
    const status = error.response?.status;
    return status === API_STATUS_CODES.UNAUTHORIZED || status === API_STATUS_CODES.FORBIDDEN;
  }

  /**
   * Check if error is a validation error
   */
  static isValidationError(error: any): boolean {
    return error.response?.status === API_STATUS_CODES.BAD_REQUEST;
  }

  /**
   * Check if error is a not found error
   */
  static isNotFoundError(error: any): boolean {
    return error.response?.status === API_STATUS_CODES.NOT_FOUND;
  }

  /**
   * Get user-friendly error message for display
   */
  static getUserFriendlyMessage(error: any): string {
    if (ErrorHandler.isNetworkError(error)) {
      return 'Please check your internet connection and try again.';
    }

    if (ErrorHandler.isAuthError(error)) {
      return 'Please log in to continue.';
    }

    if (ErrorHandler.isValidationError(error)) {
      return 'Please check your input and try again.';
    }

    if (ErrorHandler.isNotFoundError(error)) {
      return 'The requested information was not found.';
    }

    return 'Something went wrong. Please try again.';
  }

  /**
   * Handle errors in async functions with proper logging
   */
  static async handleAsyncError<T>(
    operation: () => Promise<T>,
    context: string
  ): Promise<T | null> {
    try {
      return await operation();
    } catch (error) {
      console.error(`Error in ${context}:`, error);
      
      if (error instanceof AxiosError) {
        ErrorHandler.handleAxiosError(error);
      } else {
        ErrorHandler.handleGeneralError(error as Error);
      }
      
      return null;
    }
  }
}
