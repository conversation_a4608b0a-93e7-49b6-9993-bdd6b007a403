/**
 * Centralized API Endpoints Configuration
 * 
 * This file contains all API endpoints used throughout the application.
 * This ensures consistency and makes it easy to update endpoints when needed.
 */

export const API_ENDPOINTS = {
  // Authentication endpoints
  AUTH: {
    LOGIN: '/auth/login',
    REGISTER: '/auth/register',
    FORGOT_PASSWORD: '/auth/forgot-password',
    VERIFY_RESET_OTP: '/auth/verify-reset-otp',
    RESET_PASSWORD: '/auth/reset-password',
  },

  // User-related endpoints
  USERS: {
    PROFILE: '/users/me',
    ORDERS: '/users/orders',
    ADDRESSES: '/users/addresses',
  },

  // Order endpoints
  ORDERS: {
    LIST: '/orders',
    DETAIL: (orderId: string) => `/orders/${orderId}`,
  },

  // Product endpoints
  PRODUCTS: {
    LIST: '/products',
    DETAIL: (productId: string) => `/products/${productId}`,
    SEARCH: '/products/search',
  },

  // Category endpoints
  CATEGORIES: {
    LIST: '/categories',
    DETAIL: (categoryId: string) => `/categories/${categoryId}`,
  },
} as const;

/**
 * API Response Status Codes
 */
export const API_STATUS_CODES = {
  SUCCESS: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500,
} as const;

/**
 * Common API Error Messages
 */
export const API_ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network error. Please check your connection.',
  UNAUTHORIZED: 'Please log in to continue.',
  FORBIDDEN: 'You do not have permission to perform this action.',
  NOT_FOUND: 'The requested resource was not found.',
  SERVER_ERROR: 'Server error. Please try again later.',
  VALIDATION_ERROR: 'Please check your input and try again.',
} as const;

/**
 * Helper function to get user-friendly error message based on status code
 */
export function getErrorMessage(statusCode: number): string {
  switch (statusCode) {
    case API_STATUS_CODES.UNAUTHORIZED:
      return API_ERROR_MESSAGES.UNAUTHORIZED;
    case API_STATUS_CODES.FORBIDDEN:
      return API_ERROR_MESSAGES.FORBIDDEN;
    case API_STATUS_CODES.NOT_FOUND:
      return API_ERROR_MESSAGES.NOT_FOUND;
    case API_STATUS_CODES.BAD_REQUEST:
      return API_ERROR_MESSAGES.VALIDATION_ERROR;
    case API_STATUS_CODES.INTERNAL_SERVER_ERROR:
    default:
      return API_ERROR_MESSAGES.SERVER_ERROR;
  }
}
