import {httpClient} from '../../../utils/httpClient';

export interface ResetPasswordEmailData {
  email: string;
}

export interface VerifyOTPData {
  email: string;
  otp: string;
}

export interface ResetPasswordData {
  email: string;
  otp: string;
  newPassword: string;
}

// Step 1: Send reset password email
export const sendResetPasswordEmail = async (data: ResetPasswordEmailData) => {
  return await httpClient.POST('/auth/forgot-password', data);
};

// Step 2: Verify OTP code
export const verifyResetPasswordOTP = async (data: VerifyOTPData) => {
  return await httpClient.POST('/auth/verify-reset-otp', data);
};

// Step 3: Reset password with OTP
export const resetPasswordWithOTP = async (data: ResetPasswordData) => {
  return await httpClient.POST('/auth/reset-password', data);
};
